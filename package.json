{"name": "gemini-medical-chatbot-api", "version": "1.0.0", "description": "Backend proxy for Gemini Medical Chatbot", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@google/genai": "^1.0.1", "@google/generative-ai": "^0.24.1", "@pinecone-database/pinecone": "^6.0.0", "@supabase/supabase-js": "^2.49.4", "@xenova/transformers": "^2.17.2", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "multer": "^2.0.0", "pdf-parse": "^1.1.1"}, "devDependencies": {"nodemon": "^3.0.1"}}