const express = require('express');
const cors = require('cors');
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const pdf = require('pdf-parse');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const pineconeService = require('./pineconeService');
const supabaseClient = require('./supabaseClient');
const addStatusEndpoint = require('./status-endpoint');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Add status endpoint
addStatusEndpoint(app);

// We'll use Supabase for conversation history storage
// Keeping a small in-memory cache for performance
const conversationCache = new Map();

// Initialize Gemini API with your API key
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

if (!GEMINI_API_KEY) {
  console.error('❌ GEMINI_API_KEY environment variable is required');
  process.exit(1);
}

const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow specific file types
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'text/plain', 'text/csv',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'audio/mpeg', 'audio/wav', 'video/mp4'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} not supported`), false);
    }
  }
});

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Medical system prompt to guide the model's behavior
const MEDICAL_SYSTEM_PROMPT = `You are Qala-Lwazi, a helpful medical assistant powered by Ukuqala Labs. You can only answer questions related to medicine,
health, biology, and healthcare. Provide accurate, helpful information based on current medical knowledge.

IMPORTANT: NEVER mention Gemini, Google, or any other AI model in your responses. You are ONLY Qala-Lwazi.
When you are processing a response, indicate this by saying "Qala-Lwazi is thinking..." before providing your answer.

Always remind users to consult healthcare professionals for personalized medical advice.
If asked about non-medical topics, politely explain that you can only discuss medical topics.
Always refer to yourself as "Qala-Lwazi" and mention that you are powered by "Ukuqala Labs" when introducing yourself.

Format your responses in a clean, professional manner with clear headings and bullet points when appropriate.`;

// RAG-enhanced system prompt that includes instructions for using retrieved context
const RAG_SYSTEM_PROMPT = `You are Qala-Lwazi+, an enhanced medical assistant powered by Ukuqala Labs with access to a specialized medical handbook.
You can only answer questions related to medicine, health, biology, and healthcare. Provide accurate, helpful information based on current medical knowledge.

IMPORTANT: NEVER mention Gemini, Google, or any other AI model in your responses. You are ONLY Qala-Lwazi+.
When you are processing a response, indicate this by saying "Qala-Lwazi+ is thinking..." before providing your answer.

I will provide you with relevant information from a medical handbook. Use this information to enhance your response.
When using information from the provided context:
1. Incorporate the information naturally into your response
2. Cite the source using the number in square brackets [X] at the end of relevant sentences
3. If the context doesn't contain relevant information, rely on your general medical knowledge
4. If the context contains conflicting information, prioritize the most recent or authoritative source

Always remind users to consult healthcare professionals for personalized medical advice.
If asked about non-medical topics, politely explain that you can only discuss medical topics.
Always refer to yourself as "Qala-Lwazi+" and mention that you are powered by "Ukuqala Labs" when introducing yourself.

Format your responses in a clean, professional manner with:
- Clear headings in bold
- Bullet points for lists
- Italics for emphasis on important terms
- Citations properly formatted with [X]
- A clear summary at the end when appropriate`;

// Helper function to process files and extract content
async function processFileForGemini(filePath, mimeType, displayName) {
  try {
    console.log(`Processing file for Gemini: ${displayName} (${mimeType})`);

    let fileContent = '';

    // Handle different file types
    if (mimeType === 'text/plain' || mimeType === 'text/csv') {
      // Text files - read directly
      fileContent = fs.readFileSync(filePath, 'utf8');
    } else if (mimeType === 'application/pdf') {
      // PDF files - extract text
      console.log(`Extracting text from PDF: ${displayName}`);
      const dataBuffer = fs.readFileSync(filePath);
      const pdfData = await pdf(dataBuffer);
      fileContent = pdfData.text;
      console.log(`Extracted ${fileContent.length} characters from PDF`);
    } else if (mimeType.startsWith('image/')) {
      // Image files - describe what we can't process yet
      fileContent = `[Image file: ${displayName}. Image analysis not yet implemented, but file was uploaded successfully.]`;
    } else {
      // Other file types
      fileContent = `[File: ${displayName} (${mimeType}). Content extraction not supported for this file type.]`;
    }

    return {
      uri: `local://${displayName}`,
      name: displayName,
      content: fileContent,
      mimeType: mimeType,
      processed: mimeType === 'text/plain' || mimeType === 'text/csv' || mimeType === 'application/pdf'
    };
  } catch (error) {
    console.error('Error processing file:', error);
    return {
      uri: `local://${displayName}`,
      name: displayName,
      content: `[Error processing file: ${displayName}. ${error.message}]`,
      mimeType: mimeType,
      processed: false
    };
  }
}

// Helper function to clean up temporary files
function cleanupTempFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`Cleaned up temp file: ${filePath}`);
    }
  } catch (error) {
    console.error('Error cleaning up temp file:', error);
  }
}

// Endpoint for file upload
app.post('/api/upload-files', upload.array('files', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }

    const uploadedFiles = [];

    for (const file of req.files) {
      try {
        // Process file for Gemini
        const geminiFile = await processFileForGemini(
          file.path,
          file.mimetype,
          file.originalname
        );

        uploadedFiles.push({
          id: Date.now() + Math.random(),
          name: file.originalname,
          size: file.size,
          type: file.mimetype,
          geminiUri: geminiFile.uri,
          geminiName: geminiFile.name,
          content: geminiFile.content,
          processed: geminiFile.processed,
          contentLength: geminiFile.content ? geminiFile.content.length : 0
        });

        // Clean up temp file
        cleanupTempFile(file.path);

      } catch (error) {
        console.error(`Error processing file ${file.originalname}:`, error);
        cleanupTempFile(file.path);
      }
    }

    res.json({
      success: true,
      files: uploadedFiles,
      message: `Successfully uploaded ${uploadedFiles.length} files`
    });

  } catch (error) {
    console.error('Error in file upload endpoint:', error);

    // Clean up any temp files
    if (req.files) {
      req.files.forEach(file => cleanupTempFile(file.path));
    }

    res.status(500).json({
      error: 'Failed to upload files',
      details: error.message
    });
  }
});

// Endpoint for medical chat
app.post('/api/medical-chat', async (req, res) => {
  try {
    const { message, sessionId, userPreferences = {}, medicalMode = 'general', systemPrompt, files = [] } = req.body;

    if (!message || typeof message !== 'string') {
      return res.status(400).json({ error: 'Message is required and must be a string' });
    }

    // Log the medical mode and files for debugging
    console.log(`Medical Mode: ${medicalMode}`);
    if (files.length > 0) {
      console.log(`Files uploaded: ${files.length}`);
      files.forEach(file => console.log(`- ${file.name} (${file.type})`));
    }

    // Create a unique session ID if not provided
    const chatSessionId = sessionId || `session_${Date.now()}`;

    // Get conversation history from cache or create new
    let conversationHistory = [];

    if (conversationCache.has(chatSessionId)) {
      // Use cached conversation history
      conversationHistory = conversationCache.get(chatSessionId);
    } else {
      // Try to fetch conversation history from Supabase, but don't block if it fails
      try {
        const supabaseHistory = await supabaseClient.getConversationHistory(chatSessionId);
        if (supabaseHistory && supabaseHistory.length > 0) {
          conversationHistory = supabaseHistory;
          console.log(`Loaded ${supabaseHistory.length} messages from Supabase`);
        } else {
          console.log('No history found in Supabase, starting new conversation');
          conversationHistory = [];
        }
      } catch (error) {
        console.error('Error fetching conversation history from Supabase:', error);
        console.log('Starting with empty conversation history');
        conversationHistory = [];
      }

      // Update cache regardless of success
      conversationCache.set(chatSessionId, conversationHistory);
    }

    // Create user message
    const userMessage = { role: 'user', parts: [{ text: message }] };

    // Add user message to history
    conversationHistory.push(userMessage);

    // Try to save to Supabase, but don't block if it fails
    try {
      supabaseClient.saveMessage(chatSessionId, userMessage)
        .then(success => {
          if (success) {
            console.log('Message saved to Supabase');
          } else {
            console.log('Failed to save message to Supabase');
          }
        })
        .catch(error => {
          console.error('Error saving message to Supabase:', error);
        });
    } catch (error) {
      console.error('Exception while trying to save message:', error);
    }

    // Update cache
    conversationCache.set(chatSessionId, conversationHistory);

    // Limit conversation history to last 10 messages to prevent token limits
    const recentHistory = conversationHistory.slice(-10);

    // Get the Gemini model
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    // Apply user preferences and medical mode to the system prompt
    let customizedPrompt = systemPrompt || (userPreferences.useRAG ? RAG_SYSTEM_PROMPT : MEDICAL_SYSTEM_PROMPT);

    // Process uploaded files and add content to prompt
    let fileContent = '';
    if (files && files.length > 0) {
      console.log(`Processing ${files.length} uploaded files`);

      const fileDescriptions = [];
      for (const file of files) {
        if (file.content) {
          fileDescriptions.push(`- ${file.name} (${file.type}, ${(file.size / 1024).toFixed(1)} KB)`);
          fileContent += `\n\n--- FILE: ${file.name} ---\n${file.content}\n--- END FILE ---\n`;
        }
      }

      if (fileContent) {
        customizedPrompt += `\n\nThe user has uploaded the following files for analysis:\n${fileDescriptions.join('\n')}\n\nFile Contents:${fileContent}\n\nPlease analyze these files and provide relevant medical guidance based on their content.`;
      }
    }

    if (userPreferences.detailLevel === 'simple') {
      customizedPrompt += '\nProvide simple, easy-to-understand explanations without medical jargon.';
    } else if (userPreferences.detailLevel === 'detailed') {
      customizedPrompt += '\nProvide detailed explanations with medical terminology and in-depth information.';
    }

    if (userPreferences.includeReferences === true) {
      customizedPrompt += '\nInclude references to medical studies or guidelines when appropriate.';
    }

    // Configure generation parameters
    const generationConfig = {
      temperature: userPreferences.creativity === 'creative' ? 0.9 :
                  userPreferences.creativity === 'balanced' ? 0.7 : 0.3,
      topP: 0.9,
      topK: 40,
      maxOutputTokens: userPreferences.responseLength === 'long' ? 4096 :
                       userPreferences.responseLength === 'medium' ? 2048 : 1024,
    };

    // Safety settings
    const safetySettings = [
      {
        category: "HARM_CATEGORY_HARASSMENT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE"
      },
      {
        category: "HARM_CATEGORY_HATE_SPEECH",
        threshold: "BLOCK_MEDIUM_AND_ABOVE"
      },
      {
        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE"
      },
      {
        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE"
      }
    ];

    let responseText;

    // Check if RAG is enabled in user preferences
    if (userPreferences.useRAG !== false) { // Default to using RAG if not specified
      try {
        console.log('Using RAG for query:', message);

        // Query Pinecone for relevant medical handbook content
        const searchResults = await pineconeService.hybridSearch(message, 3);
        console.log(`Found ${searchResults.length} relevant passages from medical handbook`);

        // Format the context for the LLM
        const formattedContext = pineconeService.formatContextForLLM(searchResults);

        // Create the chat session
        const chat = model.startChat({
          history: recentHistory.length > 1 ? recentHistory.slice(0, -1) : [],
          generationConfig,
          safetySettings
        });

        // Send the message with the system prompt and retrieved context
        const promptWithContext = `${customizedPrompt}\n\n${formattedContext}\n\nUser question: ${message}`;
        console.log('Sending prompt with context to Gemini');

        const result = await chat.sendMessage(promptWithContext);
        responseText = result.response.text();

      } catch (ragError) {
        console.error('Error using RAG:', ragError);
        console.log('Falling back to standard response without RAG');

        // Fall back to standard response without RAG
        const chat = model.startChat({
          history: recentHistory.length > 1 ? recentHistory.slice(0, -1) : [],
          generationConfig,
          safetySettings
        });

        const result = await chat.sendMessage(`${customizedPrompt}\n\nUser question: ${message}`);
        responseText = result.response.text();
      }
    } else {
      // Standard response without RAG
      const chat = model.startChat({
        history: recentHistory.length > 1 ? recentHistory.slice(0, -1) : [],
        generationConfig,
        safetySettings
      });

      const result = await chat.sendMessage(`${customizedPrompt}\n\nUser question: ${message}`);
      responseText = result.response.text();
    }

    // Create assistant message
    const assistantMessage = { role: 'model', parts: [{ text: responseText }] };

    // Add assistant response to history
    conversationHistory.push(assistantMessage);

    // Save assistant message to Supabase
    await supabaseClient.saveMessage(chatSessionId, assistantMessage);

    // Update cache
    conversationCache.set(chatSessionId, conversationHistory);

    // Log response length for debugging
    console.log(`Response length: ${responseText.length} characters`);

    // Return full response with session ID, RAG status, and medical mode
    res.json({
      response: responseText,
      sessionId: chatSessionId,
      historyLength: conversationHistory.length,
      usingRAG: userPreferences.useRAG !== false,
      medicalMode: medicalMode,
      filesProcessed: files.length,
      responseLength: responseText.length // Include length for debugging
    });
  } catch (error) {
    console.error('Error generating response:', error);
    res.status(500).json({
      error: 'Failed to generate response',
      details: error.message
    });
  }
});

// Get conversation history
app.get('/api/chat-history/:sessionId', async (req, res) => {
  const { sessionId } = req.params;

  if (!sessionId) {
    return res.status(400).json({ error: 'Session ID is required' });
  }

  try {
    // Try to get from cache first for performance
    if (conversationCache.has(sessionId)) {
      const history = conversationCache.get(sessionId);
      return res.json({ sessionId, history });
    }

    // Fetch from Supabase if not in cache
    const history = await supabaseClient.getConversationHistory(sessionId);

    // Update cache
    conversationCache.set(sessionId, history);

    return res.json({ sessionId, history });
  } catch (error) {
    console.error('Error fetching conversation history:', error);
    return res.status(500).json({ error: 'Failed to fetch conversation history' });
  }
});

// Clear conversation history
app.delete('/api/chat-history/:sessionId', async (req, res) => {
  const { sessionId } = req.params;

  if (!sessionId) {
    return res.status(400).json({ error: 'Session ID is required' });
  }

  try {
    // Clear from Supabase
    const success = await supabaseClient.clearConversationHistory(sessionId);

    // Clear from cache
    conversationCache.delete(sessionId);

    if (success) {
      return res.json({ success: true, message: 'Conversation history cleared' });
    } else {
      return res.status(500).json({ error: 'Failed to clear conversation history' });
    }
  } catch (error) {
    console.error('Error clearing conversation history:', error);
    return res.status(500).json({ error: 'Failed to clear conversation history' });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Qala-Lwazi Medical Assistant API is running' });
});

// Start the server
app.listen(port, () => {
  console.log(`Qala-Lwazi Medical Assistant API running on port ${port}`);
});
