// Enhanced Conversation Memory Service
// Provides better conversation flow and context management

const supabaseClient = require('./supabaseClient');

class ConversationMemoryService {
  constructor() {
    // In-memory cache for active conversations
    this.conversationCache = new Map();
    
    // Conversation context tracking
    this.contextTracking = new Map();
    
    // Maximum messages to keep in memory for context
    this.maxContextMessages = 20;
    
    // Context relevance scoring
    this.contextWeights = {
      recent: 1.0,        // Recent messages get full weight
      medical_terms: 0.8,  // Messages with medical terms
      questions: 0.7,      // Questions get higher weight
      symptoms: 0.9,       // Symptom descriptions are important
      follow_up: 1.2       // Follow-up questions get boosted weight
    };
  }

  // Enhanced session management with context tracking
  async getConversationContext(sessionId) {
    try {
      // Check cache first
      if (this.conversationCache.has(sessionId)) {
        const cached = this.conversationCache.get(sessionId);
        console.log(`Retrieved ${cached.messages.length} messages from cache for session ${sessionId}`);
        return cached;
      }

      // Fetch from Supabase
      const messages = await supabaseClient.getConversationHistory(sessionId);
      
      // Create enhanced context
      const context = {
        sessionId,
        messages: messages || [],
        summary: this.generateConversationSummary(messages || []),
        topics: this.extractTopics(messages || []),
        lastActivity: new Date(),
        language: this.detectSessionLanguage(messages || []),
        userProfile: this.buildUserProfile(messages || [])
      };

      // Cache the context
      this.conversationCache.set(sessionId, context);
      
      return context;
    } catch (error) {
      console.error('Error getting conversation context:', error);
      return {
        sessionId,
        messages: [],
        summary: '',
        topics: [],
        lastActivity: new Date(),
        language: 'english',
        userProfile: {}
      };
    }
  }

  // Add message with enhanced context tracking
  async addMessage(sessionId, message, messageType = 'user') {
    try {
      const context = await this.getConversationContext(sessionId);
      
      // Enhanced message object
      const enhancedMessage = {
        ...message,
        timestamp: new Date().toISOString(),
        messageType,
        topics: this.extractMessageTopics(message),
        sentiment: this.analyzeSentiment(message),
        medicalTerms: this.extractMedicalTerms(message),
        isFollowUp: this.isFollowUpQuestion(message, context.messages)
      };

      // Add to context
      context.messages.push(enhancedMessage);
      context.lastActivity = new Date();
      
      // Update summary and topics
      context.summary = this.generateConversationSummary(context.messages);
      context.topics = this.extractTopics(context.messages);
      context.userProfile = this.buildUserProfile(context.messages);

      // Keep only recent messages in memory
      if (context.messages.length > this.maxContextMessages) {
        context.messages = context.messages.slice(-this.maxContextMessages);
      }

      // Update cache
      this.conversationCache.set(sessionId, context);

      // Save to Supabase (async, don't wait)
      supabaseClient.saveMessage(sessionId, message)
        .then(success => {
          if (success) {
            console.log('Message saved to Supabase');
          } else {
            console.log('Failed to save message to Supabase');
          }
        })
        .catch(error => {
          console.error('Error saving message to Supabase:', error);
        });

      return context;
    } catch (error) {
      console.error('Error adding message:', error);
      throw error;
    }
  }

  // Generate conversation summary for context
  generateConversationSummary(messages) {
    if (!messages || messages.length === 0) {
      return '';
    }

    const recentMessages = messages.slice(-10); // Last 10 messages
    const topics = new Set();
    const symptoms = new Set();
    const concerns = new Set();

    recentMessages.forEach(msg => {
      if (msg.parts && msg.parts[0] && msg.parts[0].text) {
        const text = msg.parts[0].text.toLowerCase();
        
        // Extract medical topics
        const medicalKeywords = [
          'pain', 'fever', 'headache', 'cough', 'nausea', 'fatigue', 'dizziness',
          'chest pain', 'shortness of breath', 'abdominal pain', 'back pain',
          'diabetes', 'hypertension', 'heart disease', 'asthma', 'arthritis'
        ];

        medicalKeywords.forEach(keyword => {
          if (text.includes(keyword)) {
            if (keyword.includes('pain') || keyword.includes('ache') || keyword.includes('fever')) {
              symptoms.add(keyword);
            } else {
              topics.add(keyword);
            }
          }
        });

        // Extract concerns
        if (text.includes('worried') || text.includes('concerned') || text.includes('afraid')) {
          concerns.add('patient expressing concern');
        }
      }
    });

    let summary = '';
    if (topics.size > 0) {
      summary += `Topics discussed: ${Array.from(topics).join(', ')}. `;
    }
    if (symptoms.size > 0) {
      summary += `Symptoms mentioned: ${Array.from(symptoms).join(', ')}. `;
    }
    if (concerns.size > 0) {
      summary += `Patient concerns: ${Array.from(concerns).join(', ')}. `;
    }

    return summary.trim();
  }

  // Extract topics from messages
  extractTopics(messages) {
    const topics = new Set();
    const medicalTopics = [
      'cardiovascular', 'respiratory', 'digestive', 'neurological', 'musculoskeletal',
      'endocrine', 'reproductive', 'dermatological', 'psychiatric', 'pediatric',
      'geriatric', 'emergency', 'preventive care', 'nutrition', 'exercise'
    ];

    messages.forEach(msg => {
      if (msg.parts && msg.parts[0] && msg.parts[0].text) {
        const text = msg.parts[0].text.toLowerCase();
        medicalTopics.forEach(topic => {
          if (text.includes(topic) || this.isTopicRelated(text, topic)) {
            topics.add(topic);
          }
        });
      }
    });

    return Array.from(topics);
  }

  // Extract medical terms from a message
  extractMedicalTerms(message) {
    if (!message.parts || !message.parts[0] || !message.parts[0].text) {
      return [];
    }

    const text = message.parts[0].text.toLowerCase();
    const medicalTerms = [
      'diagnosis', 'treatment', 'medication', 'prescription', 'symptoms', 'condition',
      'disease', 'syndrome', 'disorder', 'infection', 'inflammation', 'chronic',
      'acute', 'therapy', 'surgery', 'procedure', 'examination', 'test', 'screening'
    ];

    return medicalTerms.filter(term => text.includes(term));
  }

  // Check if message is a follow-up question
  isFollowUpQuestion(message, previousMessages) {
    if (!message.parts || !message.parts[0] || !message.parts[0].text) {
      return false;
    }

    const text = message.parts[0].text.toLowerCase();
    const followUpIndicators = [
      'what about', 'and what if', 'but what', 'also', 'additionally', 'furthermore',
      'can you explain more', 'tell me more', 'what else', 'anything else',
      'follow up', 'related to that', 'regarding that', 'about that'
    ];

    return followUpIndicators.some(indicator => text.includes(indicator)) ||
           (previousMessages.length > 0 && this.hasContextualReference(text));
  }

  // Check for contextual references
  hasContextualReference(text) {
    const contextualWords = ['that', 'this', 'it', 'they', 'those', 'these'];
    return contextualWords.some(word => text.includes(word));
  }

  // Analyze message sentiment
  analyzeSentiment(message) {
    if (!message.parts || !message.parts[0] || !message.parts[0].text) {
      return 'neutral';
    }

    const text = message.parts[0].text.toLowerCase();
    
    const positiveWords = ['good', 'better', 'great', 'excellent', 'fine', 'well', 'healthy', 'improved'];
    const negativeWords = ['bad', 'worse', 'terrible', 'awful', 'sick', 'pain', 'hurt', 'worried', 'concerned'];
    const urgentWords = ['emergency', 'urgent', 'severe', 'critical', 'serious', 'immediate'];

    if (urgentWords.some(word => text.includes(word))) {
      return 'urgent';
    }
    
    const positiveCount = positiveWords.filter(word => text.includes(word)).length;
    const negativeCount = negativeWords.filter(word => text.includes(word)).length;

    if (negativeCount > positiveCount) {
      return 'negative';
    } else if (positiveCount > negativeCount) {
      return 'positive';
    }
    
    return 'neutral';
  }

  // Build user profile from conversation history
  buildUserProfile(messages) {
    const profile = {
      preferredLanguage: 'english',
      medicalHistory: [],
      commonConcerns: [],
      communicationStyle: 'formal',
      urgencyLevel: 'normal'
    };

    messages.forEach(msg => {
      if (msg.role === 'user' && msg.parts && msg.parts[0] && msg.parts[0].text) {
        const text = msg.parts[0].text;
        
        // Detect communication style
        if (text.includes('please') || text.includes('thank you') || text.includes('could you')) {
          profile.communicationStyle = 'polite';
        } else if (text.length < 50 && !text.includes('?')) {
          profile.communicationStyle = 'casual';
        }

        // Extract medical history mentions
        const historyIndicators = ['i have', 'i had', 'diagnosed with', 'taking medication for'];
        historyIndicators.forEach(indicator => {
          if (text.toLowerCase().includes(indicator)) {
            profile.medicalHistory.push(text);
          }
        });
      }
    });

    return profile;
  }

  // Detect session language from messages
  detectSessionLanguage(messages) {
    const languageService = require('./languageService');
    
    if (!messages || messages.length === 0) {
      return 'english';
    }

    // Analyze recent user messages
    const userMessages = messages
      .filter(msg => msg.role === 'user')
      .slice(-5); // Last 5 user messages

    const languageCounts = { english: 0, french: 0, pidgin: 0 };

    userMessages.forEach(msg => {
      if (msg.parts && msg.parts[0] && msg.parts[0].text) {
        const detectedLang = languageService.detectLanguage(msg.parts[0].text);
        languageCounts[detectedLang]++;
      }
    });

    // Return the most common language
    return Object.keys(languageCounts).reduce((a, b) => 
      languageCounts[a] > languageCounts[b] ? a : b
    );
  }

  // Check if text is related to a medical topic
  isTopicRelated(text, topic) {
    const topicKeywords = {
      cardiovascular: ['heart', 'blood pressure', 'chest', 'cardiac', 'circulation'],
      respiratory: ['lung', 'breathing', 'cough', 'asthma', 'pneumonia'],
      digestive: ['stomach', 'digestion', 'nausea', 'diarrhea', 'constipation'],
      neurological: ['brain', 'headache', 'migraine', 'seizure', 'memory'],
      musculoskeletal: ['bone', 'joint', 'muscle', 'arthritis', 'fracture']
    };

    const keywords = topicKeywords[topic] || [];
    return keywords.some(keyword => text.includes(keyword));
  }

  // Get relevant context for current query
  getRelevantContext(sessionId, currentQuery) {
    const context = this.conversationCache.get(sessionId);
    if (!context || !context.messages) {
      return '';
    }

    // Score messages based on relevance to current query
    const scoredMessages = context.messages
      .filter(msg => msg.role === 'user' || msg.role === 'model')
      .map(msg => ({
        message: msg,
        score: this.calculateRelevanceScore(msg, currentQuery, context)
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 5); // Top 5 most relevant messages

    // Format context
    let contextString = '';
    if (context.summary) {
      contextString += `Conversation Summary: ${context.summary}\n\n`;
    }

    if (scoredMessages.length > 0) {
      contextString += 'Relevant Previous Context:\n';
      scoredMessages.forEach((item, index) => {
        if (item.message.parts && item.message.parts[0] && item.message.parts[0].text) {
          contextString += `${index + 1}. ${item.message.role}: ${item.message.parts[0].text.substring(0, 200)}...\n`;
        }
      });
    }

    return contextString;
  }

  // Calculate relevance score for context
  calculateRelevanceScore(message, currentQuery, context) {
    let score = 0;

    if (!message.parts || !message.parts[0] || !message.parts[0].text) {
      return score;
    }

    const messageText = message.parts[0].text.toLowerCase();
    const queryText = currentQuery.toLowerCase();

    // Recent messages get higher score
    const messageIndex = context.messages.indexOf(message);
    const recencyScore = (messageIndex / context.messages.length) * this.contextWeights.recent;
    score += recencyScore;

    // Medical terms overlap
    const messageMedicalTerms = this.extractMedicalTerms(message);
    const queryMedicalTerms = this.extractMedicalTerms({ parts: [{ text: currentQuery }] });
    const medicalOverlap = messageMedicalTerms.filter(term => queryMedicalTerms.includes(term)).length;
    score += medicalOverlap * this.contextWeights.medical_terms;

    // Keyword similarity
    const messageWords = messageText.split(/\s+/);
    const queryWords = queryText.split(/\s+/);
    const commonWords = messageWords.filter(word => queryWords.includes(word) && word.length > 3);
    score += commonWords.length * 0.1;

    // Follow-up boost
    if (message.isFollowUp) {
      score *= this.contextWeights.follow_up;
    }

    return score;
  }

  // Clear conversation cache
  clearCache(sessionId = null) {
    if (sessionId) {
      this.conversationCache.delete(sessionId);
    } else {
      this.conversationCache.clear();
    }
  }
}

module.exports = new ConversationMemoryService();
